import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_absolute_error
from keras.models import Model
from keras.layers import Input, Dense, Dropout, LayerNormalization, MultiHeadAttention, Flatten, Add, Embedding
from keras.optimizers import Adam
from keras.callbacks import EarlyStopping, ReduceLROnPlateau
import tensorflow as tf
from haversine import haversine  # 用于计算地理距离

# ------------------ 1. 读取 Geolife Trajectories 1.3 中的 .plt 文件 ------------------
data = pd.read_csv('../../Geolife Trajectories 1.3/Data/001/Trajectory/20081024234405.plt',
                   skiprows=6, header=None)
data.columns = ['lat', 'lon', 'zero', 'alt', 'date_days', 'date', 'time']
data = data[['lat', 'lon']]
print(f'样本数：{len(data)}, 维度：{data.shape[1]}')

# ------------------ 2. 改进的数据预处理 ------------------
# 使用StandardScaler而不是MinMaxScaler，对地理坐标更合适
scaler = StandardScaler()
data_scaled = scaler.fit_transform(data)

# 添加数据增强：计算速度和方向特征
def add_velocity_features(data):
    """添加速度和方向特征"""
    data_enhanced = data.copy()

    # 计算速度（相邻点之间的距离）
    velocities = []
    directions = []

    for i in range(1, len(data)):
        # 计算距离（速度）
        dist = haversine((data.iloc[i-1]['lat'], data.iloc[i-1]['lon']),
                        (data.iloc[i]['lat'], data.iloc[i]['lon']))
        velocities.append(dist)

        # 计算方向（角度）
        lat_diff = data.iloc[i]['lat'] - data.iloc[i-1]['lat']
        lon_diff = data.iloc[i]['lon'] - data.iloc[i-1]['lon']
        direction = np.arctan2(lat_diff, lon_diff)
        directions.append(direction)

    # 第一个点的速度和方向设为0
    velocities.insert(0, 0)
    directions.insert(0, 0)

    data_enhanced['velocity'] = velocities
    data_enhanced['direction'] = directions

    return data_enhanced

# 添加特征
data_enhanced = add_velocity_features(data)
print(f'增强后的特征数：{data_enhanced.shape[1]}')

# 对增强后的数据进行标准化
scaler = StandardScaler()
data_scaled = scaler.fit_transform(data_enhanced)

# ------------------ 3. 构造数据集（增加时间步长） ------------------
def create_dataset(dataset, time_step=15):  # 增加时间步长到15
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :2])  # 只预测lat, lon
    return np.array(X), np.array(Y)

time_step = 15  # 增加时间步长
X, Y = create_dataset(data_scaled, time_step)

# ------------------ 4. 划分训练集和测试集 ------------------
train_size = int(len(X) * 0.8)
trainX, trainY = X[:train_size], Y[:train_size]
testX, testY = X[train_size:], Y[train_size:]
print('Train X shape:', trainX.shape)
print('Train Y shape:', trainY.shape)
print('Test X shape:', testX.shape)
print('Test Y shape:', testY.shape)

# ------------------ 5. 改进的 Transformer 模型搭建 ------------------

# 位置编码函数
def positional_encoding(seq_len, d_model):
    """创建位置编码"""
    pos = np.arange(seq_len)[:, np.newaxis]
    i = np.arange(d_model)[np.newaxis, :]

    angle_rads = pos / np.power(10000, (2 * (i//2)) / np.float32(d_model))

    # 对偶数索引应用sin
    angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])
    # 对奇数索引应用cos
    angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])

    pos_encoding = angle_rads[np.newaxis, ...]
    return tf.cast(pos_encoding, dtype=tf.float32)

# Transformer块
def transformer_block(inputs, head_size, num_heads, ff_dim, dropout=0.1):
    """创建一个Transformer块"""
    # Multi-head self-attention
    attention_output = MultiHeadAttention(
        num_heads=num_heads, key_dim=head_size, dropout=dropout
    )(inputs, inputs)
    attention_output = Dropout(dropout)(attention_output)
    attention_output = LayerNormalization(epsilon=1e-6)(inputs + attention_output)

    # Feed forward network
    ffn_output = Dense(ff_dim, activation="relu")(attention_output)
    ffn_output = Dense(inputs.shape[-1])(ffn_output)
    ffn_output = Dropout(dropout)(ffn_output)
    ffn_output = LayerNormalization(epsilon=1e-6)(attention_output + ffn_output)

    return ffn_output

# 构建改进的模型
input_layer = Input(shape=(time_step, data_scaled.shape[1]))

# 投影到更高维度
x = Dense(128)(input_layer)

# 添加位置编码
pos_encoding = positional_encoding(time_step, 128)
x = x + pos_encoding

# 多层Transformer块
x = transformer_block(x, head_size=32, num_heads=8, ff_dim=256, dropout=0.1)
x = transformer_block(x, head_size=32, num_heads=8, ff_dim=256, dropout=0.1)
x = transformer_block(x, head_size=32, num_heads=8, ff_dim=256, dropout=0.1)

# 全局平均池化而不是Flatten
x = tf.keras.layers.GlobalAveragePooling1D()(x)

# 输出层
x = Dense(64, activation='relu')(x)
x = Dropout(0.2)(x)
x = Dense(32, activation='relu')(x)
x = Dropout(0.1)(x)
output_layer = Dense(2, activation='linear')(x)  # 只输出lat, lon

model = Model(inputs=input_layer, outputs=output_layer)

# 使用自定义损失函数（地理距离损失）
def haversine_loss(y_true, y_pred):
    """基于Haversine距离的损失函数"""
    # 反标准化坐标
    y_true_denorm = scaler.inverse_transform(
        tf.concat([y_true, tf.zeros_like(y_true)], axis=1)
    )[:, :2]
    y_pred_denorm = scaler.inverse_transform(
        tf.concat([y_pred, tf.zeros_like(y_pred)], axis=1)
    )[:, :2]

    # 计算Haversine距离
    lat1, lon1 = y_true_denorm[:, 0], y_true_denorm[:, 1]
    lat2, lon2 = y_pred_denorm[:, 0], y_pred_denorm[:, 1]

    # 转换为弧度
    lat1, lon1, lat2, lon2 = map(tf.math.multiply, [lat1, lon1, lat2, lon2], [np.pi/180]*4)

    dlat = lat2 - lat1
    dlon = lon2 - lon1

    a = tf.math.sin(dlat/2)**2 + tf.math.cos(lat1) * tf.math.cos(lat2) * tf.math.sin(dlon/2)**2
    c = 2 * tf.math.asin(tf.math.sqrt(a))
    r = 6371  # 地球半径（公里）

    return c * r * 1000  # 转换为米

# 编译模型
model.compile(
    loss='mse',  # 先使用MSE，稍后可以尝试haversine_loss
    optimizer=Adam(learning_rate=0.0005),  # 降低学习率
    metrics=['mae']
)
model.summary()

# ------------------ 6. 改进的模型训练 ------------------
# 添加回调函数
early_stopping = EarlyStopping(
    monitor='val_loss',
    patience=15,
    restore_best_weights=True,
    verbose=1
)

reduce_lr = ReduceLROnPlateau(
    monitor='val_loss',
    factor=0.5,
    patience=8,
    min_lr=1e-6,
    verbose=1
)

callbacks = [early_stopping, reduce_lr]

# 训练模型
history = model.fit(
    trainX, trainY,
    epochs=200,  # 增加训练轮数
    batch_size=32,  # 减小批次大小
    validation_split=0.2,
    callbacks=callbacks,
    verbose=1
)

# ------------------ 7. 模型评估 ------------------
test_loss, test_mae = model.evaluate(testX, testY)
print(f'Test Loss: {test_loss:.4f}, Test MAE: {test_mae:.4f}')

# ------------------ 8. 预测 & 反归一化 ------------------
predicted = model.predict(testX)

# 反归一化预测结果（只取前2列：lat, lon）
predicted_full = np.zeros((predicted.shape[0], data_enhanced.shape[1]))
predicted_full[:, :2] = predicted
predicted_inverse = scaler.inverse_transform(predicted_full)[:, :2]

# 反归一化真实值
testY_full = np.zeros((testY.shape[0], data_enhanced.shape[1]))
testY_full[:, :2] = testY
testY_inverse = scaler.inverse_transform(testY_full)[:, :2]

# ------------------ 8.1 使用 Haversine 计算误差（米） ------------------
errors_meter = []
for true_point, pred_point in zip(testY_inverse, predicted_inverse):
    true_coord = (true_point[0], true_point[1])  # (lat, lon)
    pred_coord = (pred_point[0], pred_point[1])
    dist = haversine(true_coord, pred_coord) * 1000  # km -> m
    errors_meter.append(dist)

mean_error_meter = np.mean(errors_meter)
median_error_meter = np.median(errors_meter)
std_error_meter = np.std(errors_meter)
max_error_meter = np.max(errors_meter)
min_error_meter = np.min(errors_meter)

print(f"预测平均误差: {mean_error_meter:.2f} 米")
print(f"预测中位数误差: {median_error_meter:.2f} 米")
print(f"预测误差标准差: {std_error_meter:.2f} 米")
print(f"最大误差: {max_error_meter:.2f} 米")
print(f"最小误差: {min_error_meter:.2f} 米")

# 计算在不同阈值下的准确率
thresholds = [100, 200, 300, 500, 1000]  # 米
for threshold in thresholds:
    accuracy = np.sum(np.array(errors_meter) <= threshold) / len(errors_meter) * 100
    print(f"误差小于{threshold}米的预测占比: {accuracy:.1f}%")

# ------------------ 9. 改进的可视化 ------------------
plt.figure(figsize=(15, 5))

# 子图1：轨迹对比
plt.subplot(1, 3, 1)
plt.plot(testY_inverse[:, 1], testY_inverse[:, 0], label='True trajectory',
         marker='o', markersize=2, linestyle='-', alpha=0.7)
plt.plot(predicted_inverse[:, 1], predicted_inverse[:, 0], label='Enhanced Transformer',
         color='orange', marker='x', markersize=2, linestyle='--', alpha=0.7)
plt.legend()
plt.title('True vs Predicted Trajectory')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.grid(True, alpha=0.3)

# 子图2：误差分布
plt.subplot(1, 3, 2)
plt.hist(errors_meter, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
plt.axvline(mean_error_meter, color='red', linestyle='--', label=f'Mean: {mean_error_meter:.1f}m')
plt.axvline(median_error_meter, color='green', linestyle='--', label=f'Median: {median_error_meter:.1f}m')
plt.axvline(300, color='orange', linestyle='-', linewidth=2, label='Target: 300m')
plt.xlabel('Error (meters)')
plt.ylabel('Frequency')
plt.title('Error Distribution')
plt.legend()
plt.grid(True, alpha=0.3)

# 子图3：训练历史
plt.subplot(1, 3, 3)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.title('Training History')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig("enhanced_transformer_analysis.png", dpi=300, bbox_inches='tight')
plt.show()

# ------------------ 10. 保存改进的模型 ------------------
model.save('geolife_enhanced_transformer.h5')
print("模型已保存为 'geolife_enhanced_transformer.h5'")
