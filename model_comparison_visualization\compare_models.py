import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from keras.models import load_model
from haversine import haversine
from filterpy.kalman import KalmanFilter
import os

# ------------------ 1. 读取数据 ------------------
data = pd.read_csv('../../Geolife Trajectories 1.3\\Data\\001\\Trajectory\\20081024234405.plt', skiprows=6, header=None)
data.columns = ['lat', 'lon', 'zero', 'alt', 'date_days', 'date', 'time']
data = data[['lat', 'lon']]
print(f'样本数：{len(data)}, 维度：{data.shape[1]}')

# ------------------ 2. 数据预处理 ------------------
# 为LSTM和Kalman使用MinMaxScaler
scaler_minmax = MinMaxScaler()
data_scaled_minmax = scaler_minmax.fit_transform(data)

# 为Transformer准备增强数据和StandardScaler
def add_velocity_features(data):
    """添加速度和方向特征"""
    data_enhanced = data.copy()
    
    velocities = []
    directions = []
    
    for i in range(1, len(data)):
        dist = haversine((data.iloc[i-1]['lat'], data.iloc[i-1]['lon']),
                        (data.iloc[i]['lat'], data.iloc[i]['lon']))
        velocities.append(dist)
        
        lat_diff = data.iloc[i]['lat'] - data.iloc[i-1]['lat']
        lon_diff = data.iloc[i]['lon'] - data.iloc[i-1]['lon']
        direction = np.arctan2(lat_diff, lon_diff)
        directions.append(direction)
    
    velocities.insert(0, 0)
    directions.insert(0, 0)
    
    data_enhanced['velocity'] = velocities
    data_enhanced['direction'] = directions
    
    return data_enhanced

data_enhanced = add_velocity_features(data)
scaler_standard = StandardScaler()
data_scaled_standard = scaler_standard.fit_transform(data_enhanced)

# ------------------ 3. 构造数据集 ------------------
def create_dataset(dataset, time_step=5):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :])
    return np.array(X), np.array(Y)

def create_dataset_transformer(dataset, time_step=15):
    X, Y = [], []
    for i in range(len(dataset) - time_step):
        X.append(dataset[i:(i + time_step), :])
        Y.append(dataset[i + time_step, :2])  # 只预测lat, lon
    return np.array(X), np.array(Y)

# 为LSTM和Kalman准备数据
time_step_lstm = 5
X_lstm, Y_lstm = create_dataset(data_scaled_minmax, time_step_lstm)

# 为Transformer准备数据
time_step_transformer = 15
X_transformer, Y_transformer = create_dataset_transformer(data_scaled_standard, time_step_transformer)

# ------------------ 4. 划分测试集 ------------------
train_size_lstm = int(len(X_lstm) * 0.8)
testX_lstm, testY_lstm = X_lstm[train_size_lstm:], Y_lstm[train_size_lstm:]

train_size_transformer = int(len(X_transformer) * 0.8)
testX_transformer, testY_transformer = X_transformer[train_size_transformer:], Y_transformer[train_size_transformer:]

# ------------------ 5. 加载模型并进行预测 ------------------

# 5.1 LSTM模型预测
print("加载LSTM模型...")
try:
    lstm_model = load_model('../original_lstm/geolife_lstm_v3.h5')
    lstm_predicted = lstm_model.predict(testX_lstm)
    lstm_predicted_inverse = scaler_minmax.inverse_transform(lstm_predicted)
    lstm_testY_inverse = scaler_minmax.inverse_transform(testY_lstm)
    print("LSTM模型预测完成")
except Exception as e:
    print(f"LSTM模型加载失败: {e}")
    lstm_predicted_inverse = None

# 5.2 Kalman滤波预测
print("运行Kalman滤波...")
def kalman_predict_realistic(train_data, test_data, test_length):
    kf = KalmanFilter(dim_x=4, dim_z=2)
    dt = 1.0

    kf.F = np.array([
        [1, 0, dt, 0],
        [0, 1, 0, dt],
        [0, 0, 0.95, 0],
        [0, 0, 0, 0.95]
    ])

    kf.H = np.array([[1, 0, 0, 0],
                     [0, 1, 0, 0]])

    kf.P = np.array([[0.001, 0, 0, 0],
                     [0, 0.001, 0, 0],
                     [0, 0, 0.0001, 0],
                     [0, 0, 0, 0.0001]])

    kf.R = np.array([[0.005, 0],
                     [0, 0.005]])

    kf.Q = np.array([[dt**4/4, 0, dt**3/2, 0],
                     [0, dt**4/4, 0, dt**3/2],
                     [dt**3/2, 0, dt**2, 0],
                     [0, dt**3/2, 0, dt**2]]) * 0.0015

    trainX_lstm = X_lstm[:train_size_lstm]
    last_pos = trainX_lstm[-1, -1, :]

    if len(trainX_lstm) >= 3:
        velocities = []
        for i in range(-3, 0):
            if i < -1:
                vel = trainX_lstm[-1, i+1, :] - trainX_lstm[-1, i, :]
                velocities.append(vel)
        velocity = np.mean(velocities, axis=0) if velocities else np.array([0.0, 0.0])
    else:
        velocity = np.array([0.0, 0.0])

    kf.x = np.array([[last_pos[0]],
                     [last_pos[1]],
                     [velocity[0]],
                     [velocity[1]]])

    for i in range(max(0, len(trainX_lstm)-3), len(trainX_lstm)):
        for j in range(len(trainX_lstm[i])):
            kf.predict()
            kf.update(trainX_lstm[i, j, :])

    predicted = []
    for i in range(test_length):
        kf.predict()
        predicted.append(kf.x[:2].flatten())

        if i % 100 == 0 and i < len(test_data):
            kf.update(test_data[i])

    return np.array(predicted)

kalman_predicted = kalman_predict_realistic(X_lstm[:train_size_lstm], testY_lstm, len(testY_lstm))
kalman_predicted_inverse = scaler_minmax.inverse_transform(kalman_predicted)
print("Kalman滤波预测完成")

# 5.3 Transformer模型预测
print("加载Transformer模型...")
try:
    transformer_model = load_model('../transformer/geolife_enhanced_transformer.h5')
    transformer_predicted = transformer_model.predict(testX_transformer)
    
    # 反归一化预测结果
    predicted_full = np.zeros((transformer_predicted.shape[0], data_enhanced.shape[1]))
    predicted_full[:, :2] = transformer_predicted
    transformer_predicted_inverse = scaler_standard.inverse_transform(predicted_full)[:, :2]
    
    # 反归一化真实值
    testY_full = np.zeros((testY_transformer.shape[0], data_enhanced.shape[1]))
    testY_full[:, :2] = testY_transformer
    transformer_testY_inverse = scaler_standard.inverse_transform(testY_full)[:, :2]
    print("Transformer模型预测完成")
except Exception as e:
    print(f"Transformer模型加载失败: {e}")
    transformer_predicted_inverse = None

# ------------------ 6. 计算误差 ------------------
def calculate_errors(true_points, pred_points, model_name):
    errors_meter = []
    for true_point, pred_point in zip(true_points, pred_points):
        dist = haversine((true_point[0], true_point[1]),
                        (pred_point[0], pred_point[1])) * 1000
        errors_meter.append(dist)
    
    mean_error = np.mean(errors_meter)
    print(f"{model_name}预测平均误差: {mean_error:.2f} 米")
    return mean_error, errors_meter

# 计算各模型误差
if lstm_predicted_inverse is not None:
    lstm_mean_error, lstm_errors = calculate_errors(lstm_testY_inverse, lstm_predicted_inverse, "LSTM")

kalman_mean_error, kalman_errors = calculate_errors(lstm_testY_inverse, kalman_predicted_inverse, "Kalman")

if transformer_predicted_inverse is not None:
    transformer_mean_error, transformer_errors = calculate_errors(transformer_testY_inverse, transformer_predicted_inverse, "Transformer")

# ------------------ 7. 可视化对比 ------------------
# 设置图片尺寸为18cm x 12cm (转换为英寸: 18/2.54 x 12/2.54)
fig = plt.figure(figsize=(18/2.54, 12/2.54))

# 绘制真实轨迹
plt.plot(lstm_testY_inverse[:,1], lstm_testY_inverse[:,0], label='True trajectory', marker='o', markersize=2, linestyle='-')

# 绘制LSTM预测
if lstm_predicted_inverse is not None:
    plt.plot(lstm_predicted_inverse[:,1], lstm_predicted_inverse[:,0], label=f'LSTM', color='orange', marker='x', markersize=2, linestyle='--')

# 绘制Kalman预测
plt.plot(kalman_predicted_inverse[:,1], kalman_predicted_inverse[:,0], label=f'Kalman', color='green', linestyle='--', alpha=0.7)

# 绘制Transformer预测（需要对齐数据长度）
if transformer_predicted_inverse is not None:
    # 由于时间步长不同，需要对齐数据
    min_len = min(len(lstm_testY_inverse), len(transformer_predicted_inverse))
    plt.plot(transformer_predicted_inverse[:min_len,1], transformer_predicted_inverse[:min_len,0], label=f'Transformer', color='blue', linestyle='--', alpha=0.7)

plt.legend()
plt.title('Trajectory Prediction Comparison')
plt.xlabel('Longitude')
plt.ylabel('Latitude')

# 设置图片参数，对应MATLAB的配置
# set(gcf,'PaperUnits','centimeters')
# set(gcf,'paperSize',[18,12])
# set(gcf,'PaperpositionMode','manual')
# set(gcf,'PaperPosition',[0,0,18,12]);
# set(gcf,'Renderer','painters');
plt.tight_layout()
plt.savefig("trajectory_comparison_LSTM_Transformer_Kalman.png",
           dpi=300,
           bbox_inches='tight',
           facecolor='white',
           edgecolor='none')
plt.close()

print("\n模型对比完成！图片已保存为 'trajectory_comparison_LSTM_Transformer_Kalman.png'")
